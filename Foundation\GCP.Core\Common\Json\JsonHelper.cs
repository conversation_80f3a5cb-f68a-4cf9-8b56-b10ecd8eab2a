﻿using System.Text.Json;
using System.Text.Json.Serialization;
using System.Text.Json.Serialization.Metadata;

namespace GCP.Common
{
    public class JsonHelper
    {
        public static readonly JsonSerializerOptions SerializerOptions;
        static JsonHelper()
        {
            SerializerOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
                TypeInfoResolver = new DefaultJsonTypeInfoResolver()
                //WriteIndented = true
            };
            SerializerOptions.Converters.Add(new DateTimeConverter());
            SerializerOptions.Converters.Add(new TypeJsonConverter());
            SerializerOptions.Converters.Add(new JsonStringEnumConverter());
            SerializerOptions.Converters.Add(new BoolConverter());
            SerializerOptions.Converters.Add(new BoolToIntConverter());
            SerializerOptions.Converters.Add(new BoolToShortConverter());
            SerializerOptions.Converters.Add(new IDataParameterArrayConverter());
            SerializerOptions.Converters.Add(new FlexibleStringDictionaryConverter());
            SerializerOptions.Converters.Add(new UniversalStringConverter());

            ConvertHelper.JsonSerializerOptions = SerializerOptions;
        }

        /// <summary>
        /// 指定值序列化成字符串
        /// </summary>
        public static string Serialize(object obj, JsonSerializerOptions options = null)
        {
            return JsonSerializer.Serialize(obj, options ?? SerializerOptions);
        }
        public static byte[] SerializeToUtf8Bytes(object obj)
        {
            return JsonSerializer.SerializeToUtf8Bytes(obj, SerializerOptions);
        }
        public static async Task SerializeAsync(Stream utf8Json, object value)
        {
            await JsonSerializer.SerializeAsync(utf8Json, value, SerializerOptions);
        }


        /// <summary>
        /// 字符串反序列化成指定类型
        /// </summary>
        public static T Deserialize<T>(string value)
        {
            if (string.IsNullOrWhiteSpace(value)) return default;
            return JsonSerializer.Deserialize<T>(value, SerializerOptions);
        }
        public static ValueTask<T> DeserializeAsync<T>(Stream utf8Json)
        {
            return JsonSerializer.DeserializeAsync<T>(utf8Json, SerializerOptions);
        }

        /// <summary>
        /// 字符串反序列化成指定类型
        /// </summary>
        public static object Deserialize(string value, Type type)
        {
            return string.IsNullOrWhiteSpace(value) ? null : JsonSerializer.Deserialize(value, type, SerializerOptions);
        }
    }
}
