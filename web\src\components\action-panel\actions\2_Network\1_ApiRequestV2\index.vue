<template>
  <div>
    <t-form :data="formData" label-align="left" label-width="100px" class="form-container">
      <div class="form-header">
        <t-row :gutter="[32, 24]">
          <t-col :span="12">
            <t-form-item label="名称" prop="name">
              <t-input v-model="formData.name" placeholder="请输入名称" />
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="请求API" prop="apiId">
              <t-select
                v-model="formData.apiId"
                placeholder="请选择请求API"
                :options="apiOptions"
                @change="onApiChange"
              >
                <template #suffixIcon>
                  <t-button
                    size="small"
                    variant="text"
                    @click.stop="loadAllTemplates"
                    :disabled="!formData.apiId"
                    style="margin-right: 4px"
                  >
                    <refresh-icon />
                  </t-button>
                </template>
              </t-select>
            </t-form-item>
          </t-col>
          <t-col v-if="formData.responseId" :span="12">
            <t-form-item label="自动验证响应" prop="autoValidateResponse">
              <t-switch v-model="formData.autoValidateResponse" :disabled="!formData.responseId" />
              <div class="form-item-tip">启用后，会自动验证API响应结果，异常会报错</div>
            </t-form-item>
          </t-col>
          <t-col :span="12">
            <t-form-item label="备注" prop="description">
              <t-textarea v-model="formData.description" placeholder="请输入备注" />
            </t-form-item>
          </t-col>
        </t-row>
      </div>

      <div class="form-body">
        <!-- 请求参数配置 -->
        <action-form-title title="参数" />
        <variable-list v-model:data="formData.params" />

        <!-- 请求头配置 -->
        <action-form-title title="请求头" />
        <variable-list v-model:data="formData.headers" />

        <!-- 请求体配置 -->
        <action-form-title title="请求体" />
        <variable-list v-model:data="formData.body" />

        <!-- 输出配置 -->
        <action-form-title title="输出参数" />
        <variable-list v-model:data="currentStep.result" :show-root-node="formData.useRoot ?? false" />
      </div>
    </t-form>
  </div>
</template>

<script lang="ts">
export default {
  name: 'ApiRequestV2Actions',
};
</script>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { nextTick, onMounted, ref, watch } from 'vue';
import { RefreshIcon } from 'tdesign-icons-vue-next';
import { MessagePlugin } from 'tdesign-vue-next';

import { api, Services } from '@/api/system';
import VariableList from '@/components/action-panel//VariableList.vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { FlowData } from '@/components/action-panel/model';
import { useActionFlowStore } from '@/components/action-panel/store/index';
import { recursionMergeVariable } from '@/components/action-panel/utils';

import { useApiRequestV2Store } from './store';

const actionFlowStore = useActionFlowStore();
const apiRequestV2Store = useApiRequestV2Store();

const { currentStep } = storeToRefs(actionFlowStore);
const { args: formData } = storeToRefs(apiRequestV2Store);

const apiOptions = ref([]);
const apiList = ref([]);

watch(
  () => actionFlowStore.currentStep?.id,
  async () => {
    apiRequestV2Store.updateState();

    // 等待store初始化完成，包括响应体模板的加载
    await nextTick();
  },
  {
    immediate: true,
  },
);

watch(
  () => formData.value,
  (newValue) => {
    apiRequestV2Store.setArgs(newValue);
  },
  {
    deep: true,
  },
);

// 通过responseId加载响应体模板
const loadResponseTemplate = async (responseId: string) => {
  try {
    const responseConfig = await api.run(Services.apiGetResponseById, { id: responseId });
    if (responseConfig && responseConfig.responseData) {
      const responseTemplate = JSON.parse(responseConfig.responseData);
      if (Array.isArray(responseTemplate)) {
        // 更新独立的响应体模板变量，避免递归依赖
        apiRequestV2Store.updateResponseTemplate(responseTemplate);

        // 同时更新outputs用于配置保存
        formData.value.outputs = responseTemplate;

        // 触发setArgs来同步更新currentStep.result
        apiRequestV2Store.setArgs(formData.value);
      }
    }
  } catch (error) {
    console.error('加载响应体模板失败:', error);
    MessagePlugin.warning('加载响应体模板失败');
  }
};

onMounted(() => {
  api
    .run(Services.apiGetAll, {
      apiType: 2,
    })
    .then((res) => {
      apiList.value = res;
      apiOptions.value = res.map((item: any) => ({ label: `${item.apiName}`, value: item.id }));

      console.log('🚀 ~ .then ~ formData:', formData.value);
      if (formData.value.apiId) onApiChange(formData.value.apiId);
    });
});

// API选择变化时的处理
const onApiChange = (apiId: string) => {
  const selectedApi = apiList.value.find((api) => api.id === apiId);
  if (selectedApi) {
    loadApiTemplates(selectedApi, false); // 不强制重新加载，避免重复
  }
};

// 加载所有模板（从按钮调用）
const loadAllTemplates = () => {
  const selectedApi = apiList.value.find((api) => api.id === formData.value.apiId);
  if (!selectedApi) {
    MessagePlugin.warning('请先选择API');
    return;
  }

  loadApiTemplates(selectedApi, true); // 强制重新加载
};

// 通用的模板加载函数
const loadTemplate = (templateData: string, targetArray: FlowData[], forceReload: boolean) => {
  if (!templateData) return;

  try {
    const template = JSON.parse(templateData);
    if (Array.isArray(template)) {
      // 强制重新加载或当前没有数据时，都使用递归合并
      if (forceReload || targetArray.length === 0) {
        recursionMergeVariable(targetArray, template);
      }
    }
  } catch (error) {
    console.error('解析模板数据失败:', error);
  }
};

// 统一的API模板加载方法
const loadApiTemplates = async (apiInfo: any, forceReload = false) => {
  try {
    // 确保数组已初始化
    if (!formData.value.headers) formData.value.headers = [];
    if (!formData.value.params) formData.value.params = [];
    if (!formData.value.body) formData.value.body = [];

    // 使用通用函数加载各种模板
    loadTemplate(apiInfo.headers, formData.value.headers, forceReload);
    loadTemplate(apiInfo.queryParameters, formData.value.params, forceReload);
    loadTemplate(apiInfo.body, formData.value.body, forceReload);

    // 设置响应体配置ID
    if (apiInfo.responseId) {
      formData.value.responseId = apiInfo.responseId;

      // 通过responseId从后端获取响应体模板
      await loadResponseTemplate(apiInfo.responseId);
    } else {
      // 如果没有responseId，清空响应模板
      formData.value.outputs = [];
      apiRequestV2Store.clearResponseTemplate();
      apiRequestV2Store.setArgs(formData.value);
    }
  } catch (error) {
    console.error('加载API模板失败:', error);
    MessagePlugin.error('加载API模板失败');
  }
};
</script>

<style lang="less" scoped>
@import '@/style/form.less';
@import '@/style/table.less';

.form-header {
  width: 100%;
  padding: 16px;
}

.config-section {
  margin-bottom: 32px;
  padding: 16px;
  border: 1px solid var(--td-border-level-1-color);
  border-radius: 6px;
  background-color: var(--td-bg-color-container);

  &:last-child {
    margin-bottom: 0;
  }
}

.config-section .variable-list {
  margin-top: 12px;
}

.form-item-tip {
  font-size: 12px;
  color: var(--td-text-color-placeholder);
  margin-left: 8px;
  margin-top: 4px;
  line-height: 1.4;
}
</style>
