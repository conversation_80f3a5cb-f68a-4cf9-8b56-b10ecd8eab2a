using GCP.Iot.Interfaces;
using GCP.Iot.Models;
using Serilog;
using EasyCaching.Core;
using GCP.Common;

namespace GCP.Iot.Services
{
    /// <summary>
    /// 管理单个设备的通信任务，包括数据轮询、缓存、事件触发和生命周期管理。
    /// 经过优化，采用批处理方式确保数据一致性。
    /// </summary>
    class EquipmentCommunicationTask : IDisposable
    {
        private readonly IDriver _driver;
        private readonly IEnumerable<EquipmentVariable> _variables;
        private readonly EquipmentTypeConfig _typeConfig;
        private Task _communicationTask;
        private CancellationTokenSource _cancellationTokenSource;
        private readonly EquipmentEventManager _eventManager;
        private readonly IEasyCachingProvider _cachingProvider;

        // 缓存键前缀
        private static string CacheKeyPrefix => "Equipment:Var:";
        // 缓存过期时间（秒）。建议从外部配置中读取。
        private const int CacheExpirationSeconds = 3600;

        // --- 连接重试逻辑的参数 ---
        private int _currentRetryDelay;
        private const int InitialRetryDelayMs = 5000; // 初始重试延迟5秒
        private const int MaxRetryDelayMs = 60000 * 1;    // 最大重试延迟1分钟

        // --- 设备在线状态管理 ---
        private int _consecutiveFailures = 0;
        private const int MaxConsecutiveFailures = 2; // 连续失败2次则认为离线
        private bool _isOnline = true; // 默认在线状态
        private DateTime _lastStatusUpdate = DateTime.Now;

        public string EquipmentId { get; }

        public string EquipmentCode { get; }
        private string EquipmentType { get; }

        public bool IsConnected => _driver.IsConnected;
        public string DriverCode => _driver.DriverCode;
        public bool IsSharedDriver { get; }

        /// <summary>
        /// 获取设备在线状态
        /// </summary>
        public bool IsOnline => _isOnline;

        public EquipmentCommunicationTask(
            string equipmentId,
            string equipmentCode,
            string equipmentType,
            IDriver driver,
            IEnumerable<EquipmentVariable> variables,
            EquipmentTypeConfig typeConfig,
            EquipmentEventManager eventManager,
            bool isSharedDriver,
            IEasyCachingProvider cachingProvider)
        {
            EquipmentId = equipmentId;
            EquipmentCode = equipmentCode;
            EquipmentType = equipmentType;
            _driver = driver;
            _variables = variables;
            _typeConfig = typeConfig;
            _typeConfig.Equipments[equipmentId] = this;
            _eventManager = eventManager;
            IsSharedDriver = isSharedDriver;
            _cachingProvider = cachingProvider;
            _currentRetryDelay = InitialRetryDelayMs;

            InitializeCache();
        }

        private void InitializeCache()
        {
            // 初始化时，将变量的当前值（如果有）写入缓存
            foreach (var variable in _variables)
            {
                if (variable.CurrentValue != null)
                {
                    string cacheKey = GetVariableCacheKey(variable.VarName);
                    _cachingProvider.Set(cacheKey, variable.CurrentValue, TimeSpan.FromSeconds(CacheExpirationSeconds));
                }
            }
            // 同时更新一次ALL缓存
            UpdateEquipmentValuesCache();
        }

        private string GetVariableCacheKey(string variableName)
        {
            return $"{CacheKeyPrefix}{EquipmentId}:{variableName}";
        }

        private string GetEquipmentCacheKey()
        {
            return $"{CacheKeyPrefix}{EquipmentId}:ALL";
        }

        public Task StartAsync(CancellationToken cancellationToken)
        {
            _cancellationTokenSource = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            _communicationTask = CommunicationLoopAsync(_cancellationTokenSource.Token);
            return Task.CompletedTask;
        }

        /// <summary>
        /// 核心通信循环，经过优化，采用批处理模式。
        /// </summary>
        private async Task CommunicationLoopAsync(CancellationToken cancellationToken)
        {
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    var batchTime = DateTime.Now;
                    if (await EnsureConnectedWithRetryAsync(cancellationToken))
                    {
                        // 1. 等待一个批次的所有变量读取完成
                        var results = await ReadBatchDataAsync(_driver.SupportsBatchReading, cancellationToken);

                        // 2. 批量处理所有成功读取的结果
                        if (results.Any())
                        {
                            ProcessBatchResult(results);
                        }

                        Log.Debug("设备 {EquipmentCode} 读取数据：{EquipmentData}", EquipmentCode, results.Select(t =>
                            new[] { t.Variable.VarName, t.Result.RawValue }));
                    }
                    else
                    {
                        // 如果连接失败，更新设备离线状态
                        UpdateDeviceOnlineStatus(false);
                    }

                    // 3. 计算并执行延迟，以满足驱动的最小采样周期要求
                    var processingTime = (DateTime.Now - batchTime).TotalMilliseconds;
                    var delayTime = Math.Max(10, _driver.MinSamplingPeriod - (int)processingTime);

                    await Task.Delay(delayTime, cancellationToken);
                }
            }
            catch (OperationCanceledException)
            {
                Log.Information("设备通信任务已取消: {EquipmentCode}", EquipmentCode);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "设备通信循环异常: {EquipmentCode}", EquipmentCode);
            }
        }

        /// <summary>
        /// 读取一个批次的设备数据。
        /// 使用 Task.WhenAll 等待所有变量读取任务完成。
        /// </summary>
        public async Task<List<(EquipmentVariable Variable, DriverOperationResult Result)>> ReadBatchDataAsync(bool supportsBatchReading, CancellationToken cancellationToken)
        {
            var hasAddressList = _variables.Where(t => !string.IsNullOrEmpty(t.Address)).ToList();

            try
            {
                if (supportsBatchReading)
                {
                    var addresses = hasAddressList.ToDictionary(t => t.Address, t => t.DataTypeEnum);
                    var batchResult = await _driver.BatchReadAsync(addresses);
                    if (batchResult.RawValue is IDictionary<string, object> successResults)
                    {
                        // 批量读取成功，更新设备在线状态
                        UpdateDeviceOnlineStatus(true);
                        return successResults.Select(t =>
                        {
                            var variable = hasAddressList.FirstOrDefault(v => v.Address == t.Key);
                            if (variable == null) throw new CustomException("变量地址不匹配 " + t.Key);
                            return (variable, new DriverOperationResult
                            {
                                Status = OperationStatus.Success,
                                RawValue = t.Value
                            });
                        }).ToList();
                    }
                    throw new CustomException("批量读取失败 ");
                }

                // 创建所有变量的异步读取任务
                var readTasks = hasAddressList
                    .Select(v => ReadVariableAsync(v, cancellationToken))
                    .ToList();

                // 等待所有任务完成
                await Task.WhenAll(readTasks);

                // 筛选出成功且有返回值的任务结果并返回
                var results = readTasks
                    .Where(t => t.Result.HasValue)
                    .Select(t => t.Result.Value)
                    .ToList();

                // 如果有成功读取的结果，更新设备在线状态
                UpdateDeviceOnlineStatus(results.Any(r => r.Result.Status == OperationStatus.Success));

                return results;
            }
            catch (Exception ex)
            {
                // 读取异常，更新设备离线状态
                Log.Error(ex, "设备数据读取异常: {EquipmentCode}", EquipmentCode);
                UpdateDeviceOnlineStatus(false);
                return new List<(EquipmentVariable, DriverOperationResult)>();
            }
        }

        /// <summary>
        /// 确保设备连接，并带有指数退避的重试逻辑。
        /// </summary>
        private async Task<bool> EnsureConnectedWithRetryAsync(CancellationToken cancellationToken)
        {
            if (_driver.IsConnected)
            {
                // 如果已连接，重置重试延迟，并返回成功
                _currentRetryDelay = InitialRetryDelayMs;
                return true;
            }

            try
            {
                await _driver.ConnectAsync();
                if (_driver.IsConnected)
                {
                    Log.Information("设备已连接: {EquipmentCode}", EquipmentCode);
                    _currentRetryDelay = InitialRetryDelayMs; // 连接成功后重置延迟
                    return true;
                }

                Log.Information("设备连接失败: {EquipmentCode}，将在 {Delay}s 后重试", EquipmentCode, _currentRetryDelay / 1000);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "设备连接异常: {EquipmentCode}，将在 {Delay}s 后重试", EquipmentCode, _currentRetryDelay / 1000);
            }

            // 连接失败，执行延迟
            await Task.Delay(_currentRetryDelay, cancellationToken);
            // 增加下一次的延迟时间（指数退避）
            _currentRetryDelay = Math.Min(_currentRetryDelay * 2, MaxRetryDelayMs);
            return false;
        }

        /// <summary>
        /// 异步读取单个变量，返回可空元组。
        /// </summary>
        private async Task<(EquipmentVariable Variable, DriverOperationResult Result)?> ReadVariableAsync(
            EquipmentVariable variable, CancellationToken cancellationToken)
        {
            try
            {
                var result = await _driver.ReadAsync(variable.Address, variable.DataTypeEnum);
                result.VariableId = variable.Id;

                if (result.Status == OperationStatus.Success)
                {
                    return (variable, result);
                }

                Log.Debug("读取变量失败: {EquipmentCode}.{VarName}, 状态: {Status}", EquipmentCode, variable.VarName, result.Status);
                return null;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "读取变量异常: {EquipmentCode}.{VarName}", EquipmentCode, variable.VarName);
                return null;
            }
        }

        /// <summary>
        /// 批量处理一个采集周期的所有结果。
        /// 这是确保数据一致性的核心方法。
        /// </summary>
        private void ProcessBatchResult(List<(EquipmentVariable Variable, DriverOperationResult Result)> results)
        {
            bool anyChangeOccurred = false;

            // 步骤 1: 遍历所有结果，更新每个变量的内存值、独立缓存，并检查是否有任何变化
            foreach (var (variable, result) in results)
            {
                var oldValue = variable.CurrentValue;
                var newValue = result.RawValue;

                variable.UpdateValue(newValue);

                if (newValue != null)
                {
                    string cacheKey = GetVariableCacheKey(variable.VarName);
                    _cachingProvider.Set(cacheKey, newValue, TimeSpan.FromSeconds(CacheExpirationSeconds));
                }

                bool valueChanged = !variable.ChangeThreshold.HasValue && !variable.ArchivePeriod.HasValue ||
                                    (variable.ChangeThreshold.HasValue && ShouldTriggerChangeUpload(oldValue, newValue, variable.ChangeThreshold.Value));

                bool needsArchive = variable.ArchivePeriod is > 0 && (DateTime.Now - variable.LastArchiveTime).TotalMilliseconds >= variable.ArchivePeriod;

                if (valueChanged || needsArchive)
                {
                    anyChangeOccurred = true;
                    _eventManager.HandleVariableValueChanged(EquipmentId, variable.VarName, oldValue, newValue);

                    if (needsArchive)
                    {
                        variable.Archive(); // 更新归档时间
                        Log.Debug("归档变量数据: {EquipmentCode}.{VarName}, 值: {NewValue}", EquipmentCode, variable.VarName, newValue);
                    }
                }
            }

            // 步骤 2: 如果批次中任何一个变量发生了需要上报的变化，则检查是否需要触发设备级事件和更新ALL缓存
            if (anyChangeOccurred)
            {
                if (_typeConfig.ShouldTriggerEvent(EquipmentId, _driver.ArchivePeriod))
                {
                    // 在所有变量都更新后，再更新聚合的ALL缓存，保证数据一致性
                    UpdateEquipmentValuesCache();

                    // 触发设备数据整体变化事件
                    _eventManager.HandleEquipmentDataChanged(EquipmentId, EquipmentType, GetCurrentValues());
                }
            }
        }

        /// <summary>
        /// 更新设备所有变量的聚合缓存。
        /// </summary>
        private void UpdateEquipmentValuesCache()
        {
            var allValues = _variables.Where(v => v.CurrentValue != null).ToDictionary(v => v.VarName, v => v.CurrentValue);
            _cachingProvider.Set(GetEquipmentCacheKey(), allValues, TimeSpan.FromSeconds(CacheExpirationSeconds));
        }

        /// <summary>
        /// 安全地停止通信任务。
        /// </summary>
        public async Task StopAsync()
        {
            try
            {
                // 更新设备离线状态，但不清理事件处理器
                if (_isOnline)
                {
                    _isOnline = false;
                    _lastStatusUpdate = DateTime.Now;
                    Log.Information("设备 {EquipmentCode} 通信任务已停止，状态变更为离线", EquipmentCode);

                    // 更新缓存中的设备状态
                    var statusCacheKey = $"equipment_status_{EquipmentCode}_{EquipmentId}";
                    _cachingProvider.Set(statusCacheKey, false, TimeSpan.FromMinutes(10));
                }

                if (_cancellationTokenSource is { IsCancellationRequested: false })
                {
                    await _cancellationTokenSource.CancelAsync();
                    if (_communicationTask != null)
                    {
                        // 等待通信任务优雅地完成，或在5秒后超时
                        var completedTask = await Task.WhenAny(_communicationTask, Task.Delay(5000));

                        if (completedTask != _communicationTask)
                        {
                            Log.Warning("停止设备通信任务超时: {EquipmentCode}", EquipmentCode);
                        }
                    }
                }

                // 注意：不再清理事件处理器，保持订阅状态以便设备重新上线时立即生效
                Log.Debug("设备通信任务已停止，保持事件订阅状态: {EquipmentCode}", EquipmentCode);
            }
            catch (Exception ex)
            {
                Log.Error(ex, "停止设备通信任务时发生异常: {EquipmentCode}", EquipmentCode);
            }
            finally
            {
                // 确保在任务逻辑完全结束后才断开连接
                if (!IsSharedDriver && _driver.IsConnected)
                {
                    try
                    {
                        await _driver.DisconnectAsync();
                        Log.Information("设备已断开连接: {EquipmentCode}", EquipmentCode);
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "断开设备连接时发生异常: {EquipmentCode}", EquipmentCode);
                    }
                }
            }
        }

        public async Task<DriverOperationResult> WriteVariableAsync(string varName, object value)
        {
            var variable = _variables.FirstOrDefault(v => v.VarName == varName);
            if (variable != null)
            {
                return await WriteVariableAsync(variable.Address, value, variable.DataTypeEnum);
            }

            return new DriverOperationResult()
            {
                Status = OperationStatus.Failed,
                ErrorMessage = "写入失败：变量不存在。"
            };
        }

        public async Task<DriverOperationResult> WriteVariableAsync(string address, object value, DataTypeEnum dataType)
        {
            if (!await EnsureConnectedWithRetryAsync(CancellationToken.None))
            {
                return new DriverOperationResult()
                {
                    Status = OperationStatus.NotConnected,
                    ErrorMessage = "写入失败：设备未连接。"
                };
            }
            var result = await _driver.WriteAsync(address, value, dataType);
            if (result.Status == OperationStatus.Success)
            {
                var cacheKey = GetEquipmentCacheKey();
                await _cachingProvider.RemoveAsync(cacheKey);
            }
            return result;
        }

        public Dictionary<string, object> GetCurrentValues()
        {
            // 优先从聚合缓存中获取数据
            var cacheKey = GetEquipmentCacheKey();
            var cachedValues = _cachingProvider.Get<Dictionary<string, object>>(cacheKey);

            if (cachedValues.HasValue && cachedValues.Value.Count > 0)
            {
                return cachedValues.Value;
            }

            // 缓存未命中，从内存变量中获取，并回写缓存
            var values = _variables.Where(v => v.CurrentValue != null).ToDictionary(v => v.VarName, v => v.CurrentValue);
            _cachingProvider.Set(cacheKey, values, TimeSpan.FromSeconds(CacheExpirationSeconds));
            return values;
        }

        public object GetVariableValue(string variableName)
        {
            // 优先从单个变量缓存中获取
            string cacheKey = GetVariableCacheKey(variableName);
            var cachedValue = _cachingProvider.Get<object>(cacheKey);

            if (cachedValue.HasValue)
            {
                return cachedValue.Value;
            }

            // 缓存未命中，从内存变量中获取，并回写缓存
            var variable = _variables.FirstOrDefault(v => v.VarName == variableName);
            if (variable?.CurrentValue != null)
            {
                _cachingProvider.Set(cacheKey, variable.CurrentValue, TimeSpan.FromSeconds(CacheExpirationSeconds));
                return variable.CurrentValue;
            }

            return null;
        }

        /// <summary>
        /// 获取所有变量列表
        /// </summary>
        public IEnumerable<EquipmentVariable> GetVariables()
        {
            return _variables;
        }



        private bool ShouldTriggerChangeUpload(object oldValue, object newValue, double changeThreshold)
        {
            if (oldValue == null || newValue == null) return true;

            // 对于数值类型，计算变化是否超过阈值
            if (oldValue is IConvertible && newValue is IConvertible && oldValue.GetType().IsPrimitive && newValue.GetType().IsPrimitive)
            {
                try
                {
                    var oldDouble = Convert.ToDouble(oldValue);
                    var newDouble = Convert.ToDouble(newValue);

                    if (Math.Abs(oldDouble) < 1e-9) // 避免除以零
                        return Math.Abs(newDouble) > 1e-9;

                    var changePercent = Math.Abs((newDouble - oldDouble) / oldDouble);
                    return changePercent > changeThreshold;
                }
                catch
                {
                    return !oldValue.Equals(newValue); // 转换失败则按对象比较
                }
            }

            // 对于非数值类型，只要值不相等就触发
            return !oldValue.Equals(newValue);
        }

        /// <summary>
        /// 更新设备在线状态
        /// </summary>
        /// <param name="isSuccess">本次操作是否成功</param>
        private void UpdateDeviceOnlineStatus(bool isSuccess)
        {
            if (isSuccess)
            {
                // 操作成功，重置失败计数器，设备在线
                _consecutiveFailures = 0;
                if (!_isOnline)
                {
                    _isOnline = true;
                    _lastStatusUpdate = DateTime.Now;
                    Log.Information("设备 {EquipmentCode} 状态变更为在线", EquipmentCode);

                    // 更新缓存中的设备状态
                    var statusCacheKey = $"equipment_status_{EquipmentCode}_{EquipmentId}";
                    _cachingProvider.Set(statusCacheKey, true, TimeSpan.FromMinutes(10));
                }
            }
            else
            {
                // 操作失败，增加失败计数器
                _consecutiveFailures++;
                if (_consecutiveFailures >= MaxConsecutiveFailures && _isOnline)
                {
                    _isOnline = false;
                    _lastStatusUpdate = DateTime.Now;
                    Log.Warning("设备 {EquipmentCode} 连续 {FailureCount} 次操作失败，状态变更为离线",
                        EquipmentCode, _consecutiveFailures);

                    // 更新缓存中的设备状态
                    var statusCacheKey = $"equipment_status_{EquipmentCode}_{EquipmentId}";
                    _cachingProvider.Set(statusCacheKey, false, TimeSpan.FromMinutes(10));
                }
            }
        }

        public void Dispose()
        {
            // 只有在真正销毁设备实例时才清理事件处理器
            // 这通常发生在设备被删除或系统关闭时，而不是临时离线时
            _eventManager.ClearEquipmentHandlers(EquipmentId);

            _typeConfig.Equipments.TryRemove(EquipmentId, out _);
            _cancellationTokenSource?.Dispose();

            // 只有非共享驱动才需要释放
            if (!IsSharedDriver)
            {
                _driver?.Dispose();
            }

            Log.Debug("设备通信任务已完全释放: {EquipmentCode}", EquipmentCode);
            GC.SuppressFinalize(this);
        }
    }
}