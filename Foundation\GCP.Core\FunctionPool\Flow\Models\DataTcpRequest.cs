using GCP.Common;
using GCP.FunctionPool.Flow.Models;

namespace GCP.FunctionPool.Flow.Models
{
    /// <summary>
    /// TCP请求数据模型
    /// </summary>
    public class DataTcpRequest
    {
        /// <summary>
        /// 主机地址
        /// </summary>
        public DataValue Host { get; set; }

        /// <summary>
        /// 端口号
        /// </summary>
        public DataValue Port { get; set; }

        /// <summary>
        /// 消息内容
        /// </summary>
        public DataValue Message { get; set; }

        /// <summary>
        /// 消息格式：text, json, hex
        /// </summary>
        public string MessageFormat { get; set; } = "text";

        /// <summary>
        /// 连接超时时间（毫秒）
        /// </summary>
        public DataValue Timeout { get; set; }

        /// <summary>
        /// 编码格式：utf8, ascii, base64
        /// </summary>
        public string Encoding { get; set; } = "utf8";

        /// <summary>
        /// 是否等待响应
        /// </summary>
        public bool WaitForResponse { get; set; } = true;

        /// <summary>
        /// 响应超时时间（毫秒）
        /// </summary>
        public DataValue ResponseTimeout { get; set; }
    }
}
