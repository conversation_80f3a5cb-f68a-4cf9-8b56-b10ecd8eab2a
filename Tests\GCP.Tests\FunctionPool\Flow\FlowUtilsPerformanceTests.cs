using GCP.Common;
using GCP.DataAccess;
using GCP.FunctionPool.Flow;
using GCP.Tests.Infrastructure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Diagnostics;
using Xunit;
using Xunit.Abstractions;

namespace GCP.Core.Tests.FunctionPool.Flow
{
    public class FlowUtilsPerformanceTests : DatabaseTestBase
    {
        private readonly ServiceProvider _serviceProvider;
        private readonly FunctionContext _functionContext;
        private readonly ITestOutputHelper _output;

        public FlowUtilsPerformanceTests(ITestOutputHelper output) : base(output)
        {
            _output = output;
            var services = new ServiceCollection();
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true)
                .Build();

            services.AddSingleton<IConfiguration>(configuration);
            
            _serviceProvider = services.BuildServiceProvider();
            
            // 创建功能上下文
            _functionContext = new FunctionContext
            {
                globalData = new Dictionary<string, object>(),
            };
        }

        [Theory]
        [InlineData(1000)]
        [InlineData(5000)]
        [InlineData(10000)]
        public void BindResult_DictionaryBuilding_ShouldBeOptimized(int recordCount)
        {
            // Arrange
            var testData = GenerateTestData(recordCount);
            var engine = FlowUtils.GetEngine();
            
            var flowData = new List<FlowData>
            {
                new FlowData
                {
                    Key = "dictionary",
                    Type = "object",
                    Value = new DataValue
                    {
                        Type = "script",
                        ScriptValue = @"
                            var dic = {};
                            result.forEach(item => {
                                dic[item.mitem_code] = item.id;
                            });
                            return dic;"
                    }
                }
            };

            var globalData = new Dictionary<string, object>();
            var localVariable = new Dictionary<string, object>
            {
                { "result", testData }
            };

            // Act
            var stopwatch = Stopwatch.StartNew();
            FlowUtils.BindResult(flowData, globalData, engine, _functionContext, testData, localVariable);
            stopwatch.Stop();

            // Assert
            Assert.True(globalData.ContainsKey("dictionary"));
            var resultDic = globalData["dictionary"] as Dictionary<string, object>;
            Assert.NotNull(resultDic);
            Assert.Equal(recordCount, resultDic.Count);

            // 性能断言：优化后应该在合理时间内完成
            var maxExpectedMs = recordCount / 10; // 每1000条记录应该在100ms内完成
            
            _output.WriteLine($"处理 {recordCount} 条记录的字典构建耗时: {stopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"平均每条记录耗时: {(double)stopwatch.ElapsedMilliseconds / recordCount:F4}ms");
            
            Assert.True(stopwatch.ElapsedMilliseconds < maxExpectedMs, 
                $"性能测试失败：处理 {recordCount} 条记录耗时 {stopwatch.ElapsedMilliseconds}ms，超过预期的 {maxExpectedMs}ms");
        }

        [Fact]
        public void BindResult_CompareOptimizedVsOriginal_ShouldShowSignificantImprovement()
        {
            // Arrange
            var recordCount = 5000;
            var testData = GenerateTestData(recordCount);
            
            var optimizedFlowData = new List<FlowData>
            {
                new FlowData
                {
                    Key = "optimized_dictionary",
                    Type = "object",
                    Value = new DataValue
                    {
                        Type = "script",
                        ScriptValue = @"
                            var dic = {};
                            result.forEach(item => {
                                dic[item.mitem_code] = item.id;
                            });
                            return dic;"
                    }
                }
            };

            var originalFlowData = new List<FlowData>
            {
                new FlowData
                {
                    Key = "original_dictionary",
                    Type = "object",
                    Value = new DataValue
                    {
                        Type = "script",
                        ScriptValue = @"
                            var obj = {};
                            for (var i = 0; i < result.length; i++) {
                                var item = result[i];
                                obj[item.mitem_code] = item.id;
                            }
                            return obj;"
                    }
                }
            };

            var localVariable = new Dictionary<string, object>
            {
                { "result", testData }
            };

            // Act - 测试优化版本
            var optimizedGlobalData = new Dictionary<string, object>();
            var optimizedEngine = FlowUtils.GetEngine();
            
            var optimizedStopwatch = Stopwatch.StartNew();
            FlowUtils.BindResult(optimizedFlowData, optimizedGlobalData, optimizedEngine, _functionContext, testData, localVariable);
            optimizedStopwatch.Stop();

            // Act - 测试原始版本（使用不同的模式避免优化）
            var originalGlobalData = new Dictionary<string, object>();
            var originalEngine = FlowUtils.GetEngine();
            
            var originalStopwatch = Stopwatch.StartNew();
            FlowUtils.BindResult(originalFlowData, originalGlobalData, originalEngine, _functionContext, testData, localVariable);
            originalStopwatch.Stop();

            // Assert
            var optimizedResult = optimizedGlobalData["optimized_dictionary"] as Dictionary<string, object>;
            var originalResult = originalGlobalData["original_dictionary"] as Dictionary<string, object>;
            
            Assert.NotNull(optimizedResult);
            Assert.NotNull(originalResult);
            Assert.Equal(recordCount, optimizedResult.Count);
            Assert.Equal(recordCount, originalResult.Count);

            _output.WriteLine($"优化版本耗时: {optimizedStopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"原始版本耗时: {originalStopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"性能提升: {(double)originalStopwatch.ElapsedMilliseconds / optimizedStopwatch.ElapsedMilliseconds:F2}x");

            // 优化版本应该显著快于原始版本
            Assert.True(optimizedStopwatch.ElapsedMilliseconds < originalStopwatch.ElapsedMilliseconds,
                "优化版本应该比原始版本更快");
        }

        [Fact]
        public void BindResult_ComplexDictionaryPattern_ShouldBeOptimized()
        {
            // Arrange
            var recordCount = 2000;
            var testData = GenerateComplexTestData(recordCount);
            
            var flowData = new List<FlowData>
            {
                new FlowData
                {
                    Key = "complex_dictionary",
                    Type = "object",
                    Value = new DataValue
                    {
                        Type = "script",
                        ScriptValue = @"
                            var dic = {};
                            data.forEach(item => {
                                dic[item.code] = item.value;
                            });
                            return dic;"
                    }
                }
            };

            var globalData = new Dictionary<string, object>();
            var localVariable = new Dictionary<string, object>
            {
                { "data", testData }
            };

            var engine = FlowUtils.GetEngine();

            // Act
            var stopwatch = Stopwatch.StartNew();
            FlowUtils.BindResult(flowData, globalData, engine, _functionContext, null, localVariable);
            stopwatch.Stop();

            // Assert
            Assert.True(globalData.ContainsKey("complex_dictionary"));
            var resultDic = globalData["complex_dictionary"] as Dictionary<string, object>;
            Assert.NotNull(resultDic);
            Assert.Equal(recordCount, resultDic.Count);

            _output.WriteLine($"复杂模式处理 {recordCount} 条记录耗时: {stopwatch.ElapsedMilliseconds}ms");
            
            // 应该在合理时间内完成
            Assert.True(stopwatch.ElapsedMilliseconds < 1000, 
                $"复杂模式性能测试失败：耗时 {stopwatch.ElapsedMilliseconds}ms");
        }

        private List<Dictionary<string, object>> GenerateTestData(int count)
        {
            var data = new List<Dictionary<string, object>>();
            for (int i = 1; i <= count; i++)
            {
                data.Add(new Dictionary<string, object>
                {
                    { "id", i },
                    { "mitem_code", $"ITEM_{i:D6}" },
                    { "name", $"Item Name {i}" },
                    { "category", $"Category_{i % 10}" }
                });
            }
            return data;
        }

        private List<Dictionary<string, object>> GenerateComplexTestData(int count)
        {
            var data = new List<Dictionary<string, object>>();
            for (int i = 1; i <= count; i++)
            {
                data.Add(new Dictionary<string, object>
                {
                    { "code", $"CODE_{i:D6}" },
                    { "value", $"Value_{i}" },
                    { "description", $"Description for item {i}" },
                    { "status", i % 2 == 0 ? "active" : "inactive" }
                });
            }
            return data;
        }

        public void Dispose()
        {
            _serviceProvider?.Dispose();
        }
    }
}
