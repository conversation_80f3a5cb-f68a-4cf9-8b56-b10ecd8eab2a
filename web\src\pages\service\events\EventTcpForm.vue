<template>
  <div>
    <action-form-title title="基础配置"></action-form-title>
    <t-form ref="formRef" :data="formDataForValidation" :rules="formRules">
      <t-form-item name="port" label="端口">
        <t-input-number
          :min="1000"
          :max="65535"
          theme="normal"
          v-model="portValue"
          placeholder="请输入端口 (1000-65535)"
        />
      </t-form-item>
      <t-form-item name="hasReply" label="自动回复">
        <t-switch v-model="hasReplyFieldValue" />
      </t-form-item>
      <t-form-item name="uniqueCodeField" label="唯一编码字段">
        <t-input v-model="uniqueCodeFieldValue" placeholder="请输入唯一编码字段名，如：path 或 data.path" />
      </t-form-item>
      <t-form-item name="contentField" label="内容字段">
        <t-input v-model="contentFieldValue" placeholder="请输入内容字段名，如：data" />
      </t-form-item>
    </t-form>
    <br />
    <action-form-title title="动作绑定"></action-form-title>
    <t-space direction="vertical">
      <div>
        <t-button @click="showDialog = true"> 添加 </t-button>
      </div>
      <t-table :data="tableData" :columns="tableColumns" row-key="id" :hover="true" size="small">
        <template #operate="{ row }">
          <t-space size="small">
            <t-link theme="primary" @click="onClickBindAction(row)"> 动作 </t-link>
            <t-popconfirm content="确认删除吗" @confirm="onConfirmDelete(row.id)">
              <t-link theme="danger">删除</t-link>
            </t-popconfirm>
          </t-space>
        </template>
      </t-table>
    </t-space>

    <t-dialog v-model:visible="showDialog" header="添加动作" :on-confirm="onAddAction">
      <t-input v-model:value.trim="addText" placeholder="请输入动作代码" />
    </t-dialog>
  </div>
</template>
<script lang="ts">
export default {
  name: 'EventTcpForm',
};
</script>

<script setup lang="ts">
import { computed, ref, toRef, onMounted, watch } from 'vue';
import ActionFormTitle from '@/components/action-panel/ActionFormTitle.vue';
import { MessagePlugin, PrimaryTableCol, TableRowData, FormRule, FormInstanceFunctions } from 'tdesign-vue-next';
import { api, Services } from '@/api/system';
import { LcMessageEvent } from '@/composables/services/messageEvent';
import { core } from '@/utils/core';

const props = defineProps<{
  data: LcMessageEvent;
}>();

const formData = toRef(props, 'data');

// 表单引用
const formRef = ref<FormInstanceFunctions>();

// 获取当前设置对象
const getCurrentSettings = () => {
  const settingsStr = formData.value.settings;

  let data;
  if (settingsStr) {
    try {
      data = JSON.parse(settingsStr);
    } catch (e) {
      console.warn('Failed to parse settings:', settingsStr, e);
    }
  }
  return {
    Port: 0,
    UniqueCodeField: '',
    ContentField: '',
    HasReply: true,
    ...data,
  };
};

// 更新设置
const updateSettings = (newSettings: { Port: number; UniqueCodeField: string; ContentField: string }) => {
  const settingsStr = JSON.stringify(newSettings);
  // 直接更新 formData
  formData.value.settings = settingsStr;
};

// 端口值的双向绑定
const portValue = computed({
  get: () => {
    return getCurrentSettings().Port;
  },
  set: (val: number) => {
    const currentSettings = getCurrentSettings();
    updateSettings({
      ...currentSettings,
      Port: val,
    });
  },
});

// 唯一编码字段的双向绑定
const uniqueCodeFieldValue = computed({
  get: () => {
    return getCurrentSettings().UniqueCodeField;
  },
  set: (val: string) => {
    const currentSettings = getCurrentSettings();
    updateSettings({
      ...currentSettings,
      UniqueCodeField: val,
    });
  },
});

// 内容字段的双向绑定
const contentFieldValue = computed({
  get: () => {
    return getCurrentSettings().ContentField;
  },
  set: (val: string) => {
    const currentSettings = getCurrentSettings();
    updateSettings({
      ...currentSettings,
      ContentField: val,
    });
  },
});

// 自动回复字段的双向绑定
const hasReplyFieldValue = computed({
  get: () => {
    return getCurrentSettings().HasReply;
  },
  set: (val: boolean) => {
    const currentSettings = getCurrentSettings();
    updateSettings({
      ...currentSettings,
      HasReply: val,
    });
  },
});

// 创建用于验证的表单数据对象
const formDataForValidation = computed(() => ({
  port: portValue.value,
  uniqueCodeField: uniqueCodeFieldValue.value,
  contentField: contentFieldValue.value,
}));

// 定义验证规则
const formRules = computed<Record<string, FormRule[]>>(() => ({
  port: [
    { required: true, message: '端口不能为空', type: 'error' },
    {
      validator: (val) => val >= 1000 && val <= 65535,
      message: '端口必须在1000-65535之间',
      type: 'error',
    },
  ],
  hasReply: [{ required: true, message: '自动回复不能为空', type: 'error' }],
  uniqueCodeField: [
    { required: true, message: '唯一编码字段不能为空', type: 'error' },
    {
      min: 1,
      message: '唯一编码字段不能为空',
      type: 'error',
    },
  ],
  contentField: [
    { required: true, message: '内容字段不能为空', type: 'error' },
    {
      min: 1,
      message: '内容字段不能为空',
      type: 'error',
    },
  ],
}));

const addText = ref('');
const showDialog = ref(false);
const onAddAction = async () => {
  await api
    .run(Services.messageEventAddMapping, {
      mapping: {
        eventId: formData.value.id,
        sourceId: addText.value,
      },
      hasFunction: true,
    })
    .then(() => {
      MessagePlugin.success('添加成功');
      showDialog.value = false;
      fetchData();
    });
};

const onClickBindAction = (row: any) => {
  core.ipc.send('open-action-panel', { functionId: row.functionId, title: row.sourceId });
};

const onConfirmDelete = (id: string) => {
  api.run(Services.messageEventDeleteMapping, { id }).then(() => {
    MessagePlugin.success('删除成功');
    fetchData();
  });
};

const tableColumns = computed<PrimaryTableCol<TableRowData>[]>(() => {
  fetchData();

  return [
    {
      title: '唯一编码',
      colKey: 'sourceId',
    },
    {
      title: '操作',
      align: 'left',
      fixed: 'right',
      width: 160,
      colKey: 'operate',
    },
  ];
});

const tableData = ref<any[]>([]);
const fetchData = async () => {
  tableData.value = await api.run(Services.messageEventGetMappings, {
    eventId: formData.value.id,
  });
};

// 初始化设置
const initializeSettings = () => {
  if (!formData.value.settings) {
    console.log('EventTcpForm - 初始化默认设置');
    formData.value.settings = JSON.stringify({
      Port: 0,
      UniqueCodeField: '',
      ContentField: '',
    });
  }
};

// 调试：监听 props 和 formData 的变化
onMounted(() => {
  console.log('EventTcpForm - 组件挂载时的数据:');
  console.log('  props.data:', props.data);
  console.log('  formData.value:', formData.value);

  // 初始化设置
  initializeSettings();

  console.log('  初始化后的端口值:', portValue.value);
});

watch(
  () => props.data,
  (newData) => {
    console.log('EventTcpForm - props.data 变化:', newData);
  },
  { deep: true },
);

watch(
  () => formData.value.settings,
  (newSettings) => {
    console.log('EventTcpForm - formData.value.settings 变化:', newSettings);
  },
);

// 暴露验证方法给父组件
defineExpose({
  validate: async () => formRef.value?.validate(),
  formRef,
  getData: () => formData.value,
});
</script>
